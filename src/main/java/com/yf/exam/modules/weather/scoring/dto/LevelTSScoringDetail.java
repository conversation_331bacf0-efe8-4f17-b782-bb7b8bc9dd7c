package com.yf.exam.modules.weather.scoring.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 量级TS评分详细信息
 *
 * <AUTHOR> Assistant
 * @date 2025-01-29
 */
@Data
public class LevelTSScoringDetail {
    
    /** 降水量级 */
    private String level;
    
    /** 参与评分的站点总数 */
    private Integer totalStations;
    
    /** 考生TS评分统计 */
    private TSStatistics studentTSStats;
    
    /** CMA-MESO TS评分统计 */
    private TSStatistics cmaMesoTSStats;
    
    /** 考生基础分 */
    private Double studentBaseScore;
    
    /** 考生技巧评分 */
    private Double studentSkillScore;
    
    /** 该量级权重 */
    private Double weight;
    
    /** 该量级对最终评分的贡献 */
    private Double contributionToFinalScore;
    
    /** 特殊规则说明 */
    private String specialRuleNote;
    
    /** 参与该量级评分的站点详情 */
    private List<StationScoringDetail> stationDetails = new ArrayList<>();
    
    /**
     * TS评分统计信息
     */
    @Data
    public static class TSStatistics {
        /** 正确预报数（A） */
        private Integer correctForecast = 0;
        
        /** 错误预报数（B） */
        private Integer wrongForecast = 0;
        
        /** 漏报数（C） */
        private Integer missedForecast = 0;
        
        /** TS评分 */
        private Double tsScore = 0.0;
        
        /** TS评分计算公式说明 */
        private String formulaDescription;
        
        /** 计算TS评分 */
        public void calculateTS() {
            int total = correctForecast + wrongForecast + missedForecast;
            if (total > 0) {
                tsScore = (double) correctForecast / total;
            } else {
                tsScore = 0.0;
            }
            
            // 设置公式说明
            if (missedForecast == 0) {
                // 微量降水特殊情况，没有漏报
                formulaDescription = String.format("TS = %d / (%d + %d) = %.3f", 
                    correctForecast, correctForecast, wrongForecast, tsScore);
            } else {
                formulaDescription = String.format("TS = %d / (%d + %d + %d) = %.3f", 
                    correctForecast, correctForecast, wrongForecast, missedForecast, tsScore);
            }
        }
        
        /** 增加正确预报 */
        public void addCorrect() {
            correctForecast++;
        }
        
        /** 增加错误预报 */
        public void addWrong() {
            wrongForecast++;
        }
        
        /** 增加漏报 */
        public void addMissed() {
            missedForecast++;
        }
        
        /** 获取统计摘要 */
        public String getSummary() {
            return String.format("正确:%d, 错误:%d, 漏报:%d, TS:%.3f", 
                correctForecast, wrongForecast, missedForecast, tsScore);
        }
    }
    
    /** 计算该量级的最终贡献 */
    public void calculateContribution() {
        if (weight != null && studentSkillScore != null) {
            contributionToFinalScore = studentSkillScore * weight * 40.0;
        }
    }
    
    /** 获取评分摘要 */
    public String getSummary() {
        return String.format("%s: 站点数=%d, 学生TS=%.3f, 基础分=%.1f, 技巧分=%.3f, 权重=%.2f, 贡献=%.2f分", 
            level, totalStations, 
            studentTSStats != null ? studentTSStats.getTsScore() : 0.0,
            studentBaseScore != null ? studentBaseScore : 0.0,
            studentSkillScore != null ? studentSkillScore : 0.0,
            weight != null ? weight : 0.0,
            contributionToFinalScore != null ? contributionToFinalScore : 0.0);
    }
}
